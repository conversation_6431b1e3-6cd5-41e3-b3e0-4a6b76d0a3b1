#!/usr/bin/env python3
"""Minimal test to isolate the import issue."""

print("Testing basic imports...")

try:
    print("1. Testing pydantic...")
    from pydantic import BaseModel
    print("   ✓ pydantic works")
except Exception as e:
    print(f"   ✗ pydantic failed: {e}")

try:
    print("2. Testing openai...")
    from openai import OpenAI
    print("   ✓ openai works")
except Exception as e:
    print(f"   ✗ openai failed: {e}")

try:
    print("3. Testing dotenv...")
    from dotenv import load_dotenv
    print("   ✓ dotenv works")
except Exception as e:
    print(f"   ✗ dotenv failed: {e}")

try:
    print("4. Testing abc...")
    from abc import ABC, abstractmethod
    print("   ✓ abc works")
except Exception as e:
    print(f"   ✗ abc failed: {e}")

try:
    print("5. Testing typing...")
    from typing import Dict, List, Optional, Any, Union
    print("   ✓ typing works")
except Exception as e:
    print(f"   ✗ typing failed: {e}")

try:
    print("6. Testing os...")
    import os
    print("   ✓ os works")
except Exception as e:
    print(f"   ✗ os failed: {e}")

print("\nTesting our modules...")

try:
    print("7. Testing models module...")
    import paperscraper.models
    print("   ✓ models module imports")
except Exception as e:
    print(f"   ✗ models module failed: {e}")
    import traceback
    traceback.print_exc()

try:
    print("8. Testing inference module...")
    import paperscraper.inference
    print("   ✓ inference module imports")
except Exception as e:
    print(f"   ✗ inference module failed: {e}")
    import traceback
    traceback.print_exc()

print("Done!")
