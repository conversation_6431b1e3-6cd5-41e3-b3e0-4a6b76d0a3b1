#!/usr/bin/env python3
"""
Test script for the new LLM architecture.

Tests both the new modular approach and backward compatibility.
"""

def test_new_architecture():
    """Test the new modular architecture."""
    print("Testing new modular architecture...")
    
    try:
        # Test importing the new modules
        from paperscraper.models import OpenAILanguageModel, OllamaLanguageModel
        from paperscraper.inference import InferenceEngine, create_openai_engine, create_ollama_engine
        print("✓ Successfully imported new modules")
        
        # Test creating models (without actually calling APIs)
        try:
            # This should work even without API key for testing structure
            openai_model = OpenAILanguageModel(
                model_id="gpt-4o-mini",
                api_key="test-key",  # Dummy key for testing
                temperature=0.5
            )
            print("✓ OpenAI model creation works")
        except Exception as e:
            print(f"✗ OpenAI model creation failed: {e}")
        
        try:
            ollama_model = OllamaLanguageModel(
                model_id="llama3.1",
                temperature=0.7
            )
            print("✓ Ollama model creation works")
        except Exception as e:
            print(f"✗ Ollama model creation failed: {e}")
        
        # Test convenience functions
        try:
            engine = create_openai_engine(
                model_id="gpt-4o-mini",
                api_key="test-key",
                temperature=0.3
            )
            print("✓ OpenAI engine creation works")
        except Exception as e:
            print(f"✗ OpenAI engine creation failed: {e}")
        
        try:
            engine = create_ollama_engine(
                model_id="llama3.1",
                temperature=0.8
            )
            print("✓ Ollama engine creation works")
        except Exception as e:
            print(f"✗ Ollama engine creation failed: {e}")
            
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    
    return True


def test_backward_compatibility():
    """Test backward compatibility with the old llm.py interface."""
    print("\nTesting backward compatibility...")
    
    try:
        # Test importing from the old module
        from paperscraper.llm import getAffiliations, AuthorAffiliation, AllAuthorAffiliation, PaperAffilationAnalysis
        print("✓ Successfully imported from legacy llm module")
        
        # Test that the models are available
        try:
            # Create a dummy affiliation to test the model
            affiliation = AuthorAffiliation(author_name="Test Author", organization="Test Org")
            print(f"✓ AuthorAffiliation model works: {affiliation.author_name}")
        except Exception as e:
            print(f"✗ AuthorAffiliation model failed: {e}")
        
        try:
            all_affiliation = AllAuthorAffiliation(
                author_name="Test Author", 
                organization="Test Org", 
                is_notable=True
            )
            print(f"✓ AllAuthorAffiliation model works: {all_affiliation.is_notable}")
        except Exception as e:
            print(f"✗ AllAuthorAffiliation model failed: {e}")
        
        # Test function signature (without calling it)
        import inspect
        sig = inspect.signature(getAffiliations)
        expected_params = {'paper_title', 'paper_text', 'provider', 'api_key', 'base_url', 'model'}
        actual_params = set(sig.parameters.keys())
        
        if expected_params.issubset(actual_params):
            print("✓ getAffiliations function signature is correct")
        else:
            missing = expected_params - actual_params
            print(f"✗ getAffiliations missing parameters: {missing}")
            
    except ImportError as e:
        print(f"✗ Backward compatibility import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Backward compatibility test failed: {e}")
        return False
    
    return True


def test_model_structure():
    """Test the structure and inheritance of the new models."""
    print("\nTesting model structure...")
    
    try:
        from paperscraper.models import BaseLanguageModel, OpenAILanguageModel, OllamaLanguageModel
        from paperscraper.models import InferenceRequest, InferenceResponse
        
        # Test inheritance
        assert issubclass(OpenAILanguageModel, BaseLanguageModel), "OpenAI model should inherit from base"
        assert issubclass(OllamaLanguageModel, BaseLanguageModel), "Ollama model should inherit from base"
        print("✓ Model inheritance is correct")
        
        # Test InferenceRequest structure
        request = InferenceRequest(
            messages=[{"role": "user", "content": "test"}],
            model_id="test-model",
            temperature=0.5
        )
        assert request.messages[0]["content"] == "test"
        assert request.model_id == "test-model"
        assert request.temperature == 0.5
        print("✓ InferenceRequest structure is correct")
        
        # Test InferenceResponse structure
        response = InferenceResponse(
            content="test response",
            input_tokens=10,
            output_tokens=5,
            model_used="test-model",
            success=True
        )
        assert response.content == "test response"
        assert response.success is True
        print("✓ InferenceResponse structure is correct")
        
    except Exception as e:
        print(f"✗ Model structure test failed: {e}")
        return False
    
    return True


if __name__ == "__main__":
    print("Testing the new LLM architecture...\n")
    
    success = True
    success &= test_new_architecture()
    success &= test_backward_compatibility()
    success &= test_model_structure()
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 All tests passed! The new architecture is working correctly.")
        print("\nNext steps:")
        print("1. The old llm.py is now a compatibility layer")
        print("2. New code should use paperscraper.inference and paperscraper.models")
        print("3. Existing code using paperscraper.llm will continue to work")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    print(f"{'='*50}")
