"""
Inference utilities for paperscraper.

Handles formatting inference requests and parsing outputs for different use cases.
"""
from __future__ import annotations

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel
import os

from .models import BaseLanguageModel, InferenceRequest, InferenceResponse


class AuthorAffiliation(BaseModel):
    """Author affiliation model."""
    author_name: str
    organization: str


class AllAuthorAffiliation(BaseModel):
    """All author affiliation model with notable flag."""
    author_name: str
    organization: str
    is_notable: bool


class PaperAffilationAnalysis(BaseModel):
    """Paper affiliation analysis model."""
    paper_title: str
    all_affiliations: List[AllAuthorAffiliation]
    notable_affiliations: List[AuthorAffiliation]
    has_notable_affiliations: bool


class InferenceEngine:
    """Engine for handling inference requests and responses."""
    
    def __init__(self, language_model: BaseLanguageModel):
        """
        Initialize inference engine.

        Args:
            language_model: The language model to use for inference
        """
        self.language_model = language_model
        self.orgs_list = None  # Lazy-loaded
    
    def _load_organizations(self):
        """Load organizations list from file."""
        import os

        # Try multiple possible paths for orgs.txt
        possible_paths = [
            "orgs.txt",  # Current directory
            "../orgs.txt",  # Parent directory
            os.path.join(os.path.dirname(__file__), "..", "orgs.txt"),  # Relative to this module
            os.path.join(os.getcwd(), "orgs.txt"),  # Current working directory
        ]

        self.orgs_list = ""
        for path in possible_paths:
            try:
                with open(path, "r", encoding="utf-8") as f:
                    self.orgs_list = f.read().strip()
                    break
            except FileNotFoundError:
                continue

        if not self.orgs_list:
            print("Warning: orgs.txt not found in any expected location. Using empty organizations list.")
    
    def format_affiliation_request(
        self,
        paper_title: str,
        paper_text: str,
        model_id: Optional[str] = None,
        temperature: Optional[float] = None
    ) -> InferenceRequest:
        """
        Format an affiliation extraction request.

        Args:
            paper_title: Title of the paper
            paper_text: Text content of the paper
            model_id: Optional model ID override
            temperature: Optional temperature override

        Returns:
            InferenceRequest object
        """
        # Lazy-load organizations list
        if self.orgs_list is None:
            self._load_organizations()

        prompt = f"""
You are tasked with extracting ALL author affiliations from a research paper and determining which ones match the provided notable organizations.

Paper title: {paper_title}

First page text: {paper_text}

Notable organizations to check against:
{self.orgs_list}

Extract ALL author affiliations from the paper, then:
1. Mark each affiliation as notable (is_notable: true) if it matches any organization in the provided list
2. Include all affiliations in all_affiliations
3. Include only notable affiliations in notable_affiliations
4. Set has_notable_affiliations to true if any affiliations match the notable organizations list

Return comprehensive affiliation data for all authors.
"""
        
        messages = [
            {
                "role": "system",
                "content": "Extract ALL author affiliations from research papers and identify which match notable organizations."
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        return InferenceRequest(
            messages=messages,
            model_id=model_id,
            response_format=PaperAffilationAnalysis,
            temperature=temperature
        )
    
    def process_affiliation_requests(
        self,
        requests: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Process multiple affiliation extraction requests.
        
        Args:
            requests: List of request dictionaries with keys:
                - paper_title: str
                - paper_text: str
                - model_id: Optional[str]
                - temperature: Optional[float]
        
        Returns:
            List of response dictionaries with keys:
                - input_tokens: int
                - output_tokens: int
                - affiliations: dict (parsed PaperAffilationAnalysis)
                - success: bool
                - error: Optional[str]
        """
        # Format requests
        inference_requests = []
        for req in requests:
            inference_req = self.format_affiliation_request(
                paper_title=req["paper_title"],
                paper_text=req["paper_text"],
                model_id=req.get("model_id"),
                temperature=req.get("temperature")
            )
            inference_requests.append(inference_req)
        
        # Process through language model
        responses = self.language_model.infer(inference_requests)
        
        # Parse and format responses
        formatted_responses = []
        for response in responses:
            if response.success and response.parsed_content:
                formatted_response = {
                    "input_tokens": response.input_tokens,
                    "output_tokens": response.output_tokens,
                    "affiliations": response.parsed_content,
                    "success": True,
                    "error": None
                }
            else:
                formatted_response = {
                    "input_tokens": response.input_tokens,
                    "output_tokens": response.output_tokens,
                    "affiliations": None,
                    "success": False,
                    "error": response.error
                }
            formatted_responses.append(formatted_response)
        
        return formatted_responses
    
    def get_affiliations(
        self,
        paper_title: str,
        paper_text: str,
        model_id: Optional[str] = None,
        temperature: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Extract affiliations from a single paper.
        
        Args:
            paper_title: Title of the paper
            paper_text: Text content of the paper
            model_id: Optional model ID override
            temperature: Optional temperature override
            
        Returns:
            Dictionary with affiliation analysis results
        """
        request = {
            "paper_title": paper_title,
            "paper_text": paper_text,
            "model_id": model_id,
            "temperature": temperature
        }
        
        responses = self.process_affiliation_requests([request])
        return responses[0]
    
    def format_custom_request(
        self,
        messages: List[Dict[str, str]],
        response_format: Optional[Any] = None,
        model_id: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> InferenceRequest:
        """
        Format a custom inference request.
        
        Args:
            messages: List of message dictionaries
            response_format: Optional structured response format
            model_id: Optional model ID override
            temperature: Optional temperature override
            max_tokens: Optional max tokens override
            
        Returns:
            InferenceRequest object
        """
        return InferenceRequest(
            messages=messages,
            model_id=model_id,
            response_format=response_format,
            temperature=temperature,
            max_tokens=max_tokens
        )
    
    def process_custom_requests(
        self,
        requests: List[InferenceRequest]
    ) -> List[InferenceResponse]:
        """
        Process custom inference requests.
        
        Args:
            requests: List of InferenceRequest objects
            
        Returns:
            List of InferenceResponse objects
        """
        return self.language_model.infer(requests)


# Convenience functions for backward compatibility
def create_openai_engine(
    model_id: str = "gpt-4o-mini",
    api_key: Optional[str] = None,
    base_url: str = "https://api.openai.com/v1",
    temperature: float = 0.7,
    **kwargs
) -> InferenceEngine:
    """Create an inference engine with OpenAI language model."""
    from .models import OpenAILanguageModel
    
    model = OpenAILanguageModel(
        model_id=model_id,
        api_key=api_key,
        base_url=base_url,
        temperature=temperature,
        **kwargs
    )
    return InferenceEngine(model)


def create_ollama_engine(
    model_id: str = "llama3.1",
    api_key: str = "ollama",
    base_url: str = "http://localhost:11434/v1",
    temperature: float = 0.7,
    **kwargs
) -> InferenceEngine:
    """Create an inference engine with Ollama language model."""
    from .models import OllamaLanguageModel
    
    model = OllamaLanguageModel(
        model_id=model_id,
        api_key=api_key,
        base_url=base_url,
        temperature=temperature,
        **kwargs
    )
    return InferenceEngine(model)


# Legacy function for backward compatibility
def getAffiliations(
    paper_title: str,
    paper_text: str,
    *,
    provider: str = "openai",
    api_key: Optional[str] = None,
    base_url: Optional[str] = None,
    model: Optional[str] = None
) -> Dict:
    """
    Legacy function for backward compatibility.
    
    Args:
        paper_title: Title of the paper
        paper_text: Text content of the paper
        provider: Provider name ("openai" or "ollama")
        api_key: API key
        base_url: Base URL
        model: Model ID
        
    Returns:
        Dictionary with affiliation analysis results
    """
    # Set default model based on environment or provider
    if model is None:
        model = os.getenv("AFFILIATIONS_MODEL", "gpt-4o-mini" if provider == "openai" else "llama3.1")
    
    # Create appropriate engine
    if provider.lower() == "ollama":
        engine = create_ollama_engine(
            model_id=model,
            api_key=api_key or "ollama",
            base_url=base_url or "http://localhost:11434/v1"
        )
    else:
        engine = create_openai_engine(
            model_id=model,
            api_key=api_key,
            base_url=base_url or "https://api.openai.com/v1"
        )
    
    return engine.get_affiliations(paper_title, paper_text)
