"""
Language model implementations for paperscraper.

Provides base class and specific implementations for different LLM providers.
"""
from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel
from openai import OpenAI
import os
from dotenv import load_dotenv

load_dotenv()


class InferenceRequest(BaseModel):
    """Single inference request parameters."""
    messages: List[Dict[str, str]]
    model_id: Optional[str] = None
    response_format: Optional[Any] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None


class InferenceResponse(BaseModel):
    """Single inference response."""
    content: Optional[str] = None
    parsed_content: Optional[Dict] = None
    input_tokens: int = 0
    output_tokens: int = 0
    model_used: str = ""
    success: bool = True
    error: Optional[str] = None


class BaseLanguageModel(ABC):
    """Base class for language model implementations."""
    
    def __init__(
        self,
        model_id: str,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ):
        """
        Initialize the language model.
        
        Args:
            model_id: The model identifier
            api_key: API key for authentication
            base_url: Base URL for the API
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional model-specific parameters
        """
        self.model_id = model_id
        self.api_key = api_key
        self.base_url = base_url
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.extra_params = kwargs
        
        # Initialize the client
        self._client = self._initialize_client()
    
    @abstractmethod
    def _initialize_client(self) -> Any:
        """Initialize the API client."""
        pass
    
    @abstractmethod
    def _process_single_prompt(
        self,
        messages: List[Dict[str, str]],
        response_format: Optional[Any] = None,
        **kwargs
    ) -> InferenceResponse:
        """
        Process a single prompt.
        
        Args:
            messages: List of message dictionaries
            response_format: Optional structured response format
            **kwargs: Additional parameters
            
        Returns:
            InferenceResponse object
        """
        pass
    
    def infer(self, requests: List[InferenceRequest]) -> List[InferenceResponse]:
        """
        Process multiple inference requests.
        
        Args:
            requests: List of InferenceRequest objects
            
        Returns:
            List of InferenceResponse objects
        """
        responses = []
        
        for request in requests:
            try:
                # Use request-specific model_id if provided, otherwise use instance default
                model_id = request.model_id or self.model_id
                
                # Merge parameters
                kwargs = {
                    'temperature': request.temperature or self.temperature,
                    'max_tokens': request.max_tokens or self.max_tokens,
                    'model_id': model_id,
                    **self.extra_params
                }
                
                response = self._process_single_prompt(
                    messages=request.messages,
                    response_format=request.response_format,
                    **kwargs
                )
                responses.append(response)
                
            except Exception as e:
                error_response = InferenceResponse(
                    success=False,
                    error=str(e),
                    model_used=request.model_id or self.model_id
                )
                responses.append(error_response)
        
        return responses


class OpenAILanguageModel(BaseLanguageModel):
    """OpenAI language model implementation."""
    
    def __init__(
        self,
        model_id: str = "gpt-4o-mini",
        api_key: Optional[str] = None,
        base_url: str = "https://api.openai.com/v1",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ):
        """Initialize OpenAI language model."""
        # Get API key from environment if not provided
        if api_key is None:
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                raise RuntimeError("OPENAI_API_KEY is required for OpenAI provider.")
        
        super().__init__(
            model_id=model_id,
            api_key=api_key,
            base_url=base_url,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
    
    def _initialize_client(self) -> OpenAI:
        """Initialize OpenAI client."""
        return OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
    
    def _process_single_prompt(
        self,
        messages: List[Dict[str, str]],
        response_format: Optional[Any] = None,
        **kwargs
    ) -> InferenceResponse:
        """Process a single prompt using OpenAI API."""
        try:
            model_id = kwargs.get('model_id', self.model_id)
            temperature = kwargs.get('temperature', self.temperature)
            max_tokens = kwargs.get('max_tokens', self.max_tokens)
            
            # Prepare completion parameters
            completion_params = {
                'model': model_id,
                'messages': messages,
                'temperature': temperature,
            }
            
            if max_tokens:
                completion_params['max_tokens'] = max_tokens
            
            # Use structured output if response_format is provided
            if response_format:
                completion = self._client.beta.chat.completions.parse(
                    response_format=response_format,
                    **completion_params
                )
                
                return InferenceResponse(
                    parsed_content=completion.choices[0].message.parsed.model_dump(),
                    input_tokens=completion.usage.prompt_tokens,
                    output_tokens=completion.usage.completion_tokens,
                    model_used=model_id,
                    success=True
                )
            else:
                completion = self._client.chat.completions.create(**completion_params)
                
                return InferenceResponse(
                    content=completion.choices[0].message.content,
                    input_tokens=completion.usage.prompt_tokens,
                    output_tokens=completion.usage.completion_tokens,
                    model_used=model_id,
                    success=True
                )
                
        except Exception as e:
            return InferenceResponse(
                success=False,
                error=str(e),
                model_used=kwargs.get('model_id', self.model_id)
            )


class OllamaLanguageModel(BaseLanguageModel):
    """Ollama language model implementation."""
    
    def __init__(
        self,
        model_id: str = "llama3.1",
        api_key: str = "ollama",
        base_url: str = "http://localhost:11434/v1",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ):
        """Initialize Ollama language model."""
        super().__init__(
            model_id=model_id,
            api_key=api_key,
            base_url=base_url,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
    
    def _initialize_client(self) -> OpenAI:
        """Initialize Ollama client (using OpenAI-compatible interface)."""
        return OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
    
    def _process_single_prompt(
        self,
        messages: List[Dict[str, str]],
        response_format: Optional[Any] = None,
        **kwargs
    ) -> InferenceResponse:
        """Process a single prompt using Ollama API."""
        try:
            model_id = kwargs.get('model_id', self.model_id)
            temperature = kwargs.get('temperature', self.temperature)
            max_tokens = kwargs.get('max_tokens', self.max_tokens)
            
            # Prepare completion parameters
            completion_params = {
                'model': model_id,
                'messages': messages,
                'temperature': temperature,
            }
            
            if max_tokens:
                completion_params['max_tokens'] = max_tokens
            
            # Use structured output if response_format is provided
            if response_format:
                completion = self._client.beta.chat.completions.parse(
                    response_format=response_format,
                    **completion_params
                )
                
                return InferenceResponse(
                    parsed_content=completion.choices[0].message.parsed.model_dump(),
                    input_tokens=completion.usage.prompt_tokens if completion.usage else 0,
                    output_tokens=completion.usage.completion_tokens if completion.usage else 0,
                    model_used=model_id,
                    success=True
                )
            else:
                completion = self._client.chat.completions.create(**completion_params)
                
                return InferenceResponse(
                    content=completion.choices[0].message.content,
                    input_tokens=completion.usage.prompt_tokens if completion.usage else 0,
                    output_tokens=completion.usage.completion_tokens if completion.usage else 0,
                    model_used=model_id,
                    success=True
                )
                
        except Exception as e:
            return InferenceResponse(
                success=False,
                error=str(e),
                model_used=kwargs.get('model_id', self.model_id)
            )
