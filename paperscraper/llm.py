"""
LLM processing for extracting affiliations.

DEPRECATED: This module is maintained for backward compatibility.
New code should use the inference.py and models.py modules directly.

Provides getAffiliations that uses the OpenAI client with selectable provider:
- provider="openai": base_url https://api.openai.com/v1 (requires OPENAI_API_KEY)
- provider="ollama": base_url http://localhost:11434/v1 (api_key can be any string, e.g., "ollama")

Returns a dict with input_tokens, output_tokens, and affiliations (pydantic model serialized to dict).
"""
from __future__ import annotations

from typing import Dict, Optional
import warnings

# Import from the new modules
from .inference import getAffiliations as _getAffiliations

# Re-export models for backward compatibility
from .inference import AuthorAffiliation, AllAuthorAffiliation, PaperAffilationAnalysis

# Legacy constants for backward compatibility
OLLAMA_BASE = "http://localhost:11434/v1"
OPENAI_BASE = "https://api.openai.com/v1"

# Deprecation warning is issued when functions are called, not at import time


def getAffiliations(
    paper_title: str,
    paper_text: str,
    *,
    provider: str = "openai",
    api_key: Optional[str] = None,
    base_url: Optional[str] = None,
    model: Optional[str] = None
) -> Dict:
    """
    Legacy function - delegates to the new inference module.

    Args:
        paper_title: Title of the paper
        paper_text: Text content of the paper
        provider: Provider name ("openai" or "ollama")
        api_key: API key
        base_url: Base URL
        model: Model ID

    Returns:
        Dictionary with affiliation analysis results
    """
    return _getAffiliations(
        paper_title=paper_title,
        paper_text=paper_text,
        provider=provider,
        api_key=api_key,
        base_url=base_url,
        model=model
    )


# Legacy functions for backward compatibility
def _get_client(provider: str = "openai", api_key: Optional[str] = None, base_url: Optional[str] = None):
    """Deprecated: Use models.py classes instead."""
    warnings.warn(
        "_get_client is deprecated. Use OpenAILanguageModel or OllamaLanguageModel instead.",
        DeprecationWarning,
        stacklevel=2
    )
    from openai import OpenAI
    import os

    provider = (provider or "openai").lower()
    if provider == "ollama":
        return OpenAI(base_url=base_url or OLLAMA_BASE, api_key="ollama")
    # default openai
    key = api_key or os.getenv("OPENAI_API_KEY")
    if not key:
        raise RuntimeError("OPENAI_API_KEY is required for provider 'openai'.")
    return OpenAI(base_url=base_url or OPENAI_BASE, api_key=key)


def parse_output(completion, provider):
    """Deprecated: Use inference.py InferenceEngine instead."""
    warnings.warn(
        "parse_output is deprecated. Use InferenceEngine from inference.py instead.",
        DeprecationWarning,
        stacklevel=2
    )

    if provider in {"openai", "ollama"}:
        return {
            "input_tokens": completion.usage.prompt_tokens,
            "output_tokens": completion.usage.completion_tokens,
            "affiliations": completion.choices[0].message.parsed.model_dump()
        }

    return {}
